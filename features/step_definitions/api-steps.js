const { Given, When, Then } = require('@cucumber/cucumber');
const axios = require('axios');
const { expect } = require('chai');

// API testing steps
Given('I have valid admin credentials', function () {
  this.adminUsername = process.env.KEYCLOAK_ADMIN || 'admin';
  this.adminPassword = process.env.KEYCLOAK_ADMIN_PASSWORD || 'admin';
  this.keycloakBaseUrl = process.env.KEYCLOAK_BASE_URL || 'http://localhost:8080';
  
  expect(this.adminUsername).to.not.be.empty;
  expect(this.adminPassword).to.not.be.empty;
});

When('I make a GET request to {string}', async function (endpoint) {
  try {
    // First, get an access token
    const tokenResponse = await axios.post(
      `${this.keycloakBaseUrl}/realms/master/protocol/openid-connect/token`,
      new URLSearchParams({
        grant_type: 'password',
        client_id: 'admin-cli',
        username: this.adminUsername,
        password: this.adminPassword,
      }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    );

    this.accessToken = tokenResponse.data.access_token;

    // Make the actual API request
    this.apiResponse = await axios.get(
      `${this.keycloakBaseUrl}${endpoint}`,
      {
        headers: {
          Authorization: `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    this.apiError = error;
    this.apiResponse = error.response;
  }
});

Then('the response status should be {int}', function (expectedStatus) {
  expect(this.apiResponse).to.exist;
  expect(this.apiResponse.status).to.equal(expectedStatus);
});

Then('the response should contain realm information', function () {
  expect(this.apiResponse.data).to.be.an('object');
  expect(this.apiResponse.data).to.have.property('realm');
  expect(this.apiResponse.data).to.have.property('enabled');
  expect(this.apiResponse.data.realm).to.equal('master');
});

// Additional step definitions for comprehensive API testing
Then('the response should contain a list of realms', function () {
  expect(this.apiResponse.data).to.be.an('array');
  if (this.apiResponse.data.length > 0) {
    expect(this.apiResponse.data[0]).to.have.property('realm');
    expect(this.apiResponse.data[0]).to.have.property('enabled');
  }
});

Then('the response should contain a list of users', function () {
  expect(this.apiResponse.data).to.be.an('array');
  // Users array might be empty, so we just check it's an array
});

Then('the response should contain a list of clients', function () {
  expect(this.apiResponse.data).to.be.an('array');
  if (this.apiResponse.data.length > 0) {
    expect(this.apiResponse.data[0]).to.have.property('clientId');
    expect(this.apiResponse.data[0]).to.have.property('enabled');
  }
});

When('I make a POST request to {string} with data:', async function (endpoint, docString) {
  try {
    const requestData = JSON.parse(docString);
    
    this.apiResponse = await axios.post(
      `${this.keycloakBaseUrl}${endpoint}`,
      requestData,
      {
        headers: {
          Authorization: `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    this.apiError = error;
    this.apiResponse = error.response;
  }
});

Then('the user should be created successfully', function () {
  expect(this.apiResponse.status).to.equal(201);
  expect(this.apiResponse.headers).to.have.property('location');
});

When('I make an unauthorized GET request to {string}', async function (endpoint) {
  try {
    this.apiResponse = await axios.get(
      `${this.keycloakBaseUrl}${endpoint}`,
      {
        headers: {
          'Content-Type': 'application/json',
        },
        // No Authorization header - should be unauthorized
      }
    );
  } catch (error) {
    this.apiError = error;
    this.apiResponse = error.response;
  }
});
