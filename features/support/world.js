const { setWorldConstructor, setDefaultTimeout } = require('@cucumber/cucumber');

class CustomWorld {
  constructor() {
    this.variables = {};
    this.keycloakBaseUrl = process.env.KEYCLOAK_BASE_URL || 'http://localhost:8080';
    this.adminUsername = process.env.KEYCLOAK_ADMIN || 'admin';
    this.adminPassword = process.env.KEYCLOAK_ADMIN_PASSWORD || 'admin';
  }

  setVariable(key, value) {
    this.variables[key] = value;
  }

  getVariable(key) {
    return this.variables[key];
  }

  async waitFor(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

setWorldConstructor(CustomWorld);

// Set default timeout to 30 seconds
setDefaultTimeout(30 * 1000);
