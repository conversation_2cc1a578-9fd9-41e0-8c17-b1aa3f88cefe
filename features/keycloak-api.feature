Feature: Keycloak REST API Testing
  As a developer
  I want to test Keycloak REST API endpoints
  So that I can verify API functionality and integration

  Background:
    Given I have valid admin credentials

  @api
  Scenario: Get master realm information
    When I make a GET request to "/admin/realms/master"
    Then the response status should be 200
    And the response should contain realm information

  @api
  Scenario: List all realms
    When I make a GET request to "/admin/realms"
    Then the response status should be 200
    And the response should contain a list of realms

  @api
  Scenario: Get users in master realm
    When I make a GET request to "/admin/realms/master/users"
    Then the response status should be 200
    And the response should contain a list of users

  @api
  Scenario: Get clients in master realm
    When I make a GET request to "/admin/realms/master/clients"
    Then the response status should be 200
    And the response should contain a list of clients

  @api
  Scenario: Create a new user via API
    When I make a POST request to "/admin/realms/master/users" with data:
      """
      {
        "username": "testuser-api",
        "email": "<EMAIL>",
        "firstName": "Test",
        "lastName": "User",
        "enabled": true
      }
      """
    Then the response status should be 201
    And the user should be created successfully

  @api
  Scenario: Handle invalid API request
    When I make a GET request to "/admin/realms/nonexistent"
    Then the response status should be 404

  @api
  Scenario: Unauthorized request without token
    When I make an unauthorized GET request to "/admin/realms/master"
    Then the response status should be 401
