# Database Scripts

This directory contains scripts for backing up and restoring the PostgreSQL database running in Docker.

## Scripts

### `load-env.sh`
Loads environment variables from a .env file and exports them to your shell.

**Usage:**
```bash
# Load from default .env file
source scripts/load-env.sh

# Load from specific file
source scripts/load-env.sh .env.production
source scripts/load-env.sh config/dev.env
```

**Note:** This script must be **sourced** (not executed) to export variables to your current shell session.

### `backup-postgres.sh`
Creates a backup of the PostgreSQL database.

**Usage:**
```bash
# Set environment variables first
source scripts/load-env.sh

# Then run backup
./scripts/backup-postgres.sh
```

### `restore-postgres.sh`
Restores a PostgreSQL database from a backup file.

**Usage:**
```bash
# Set environment variables first
source scripts/load-env.sh

# List available backups
./scripts/restore-postgres.sh -l

# Restore from backup
./scripts/restore-postgres.sh postgres_backup_20250706_143022.sql.gz

# Force restore without confirmation
./scripts/restore-postgres.sh -f backup_file.sql.gz
```

## Environment Variables

The scripts use these environment variables:

- `POSTGRES_DB` - Database name (default: keycloak)
- `POSTGRES_USER` - Database user (default: postgres) 
- `POSTGRES_PASSWORD` - Database password (default: password)

## Setup

1. **Copy the example environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Edit the .env file with your actual values:**
   ```bash
   vi .env
   ```

3. **Load the environment and run scripts:**
   ```bash
   # Load environment
   source scripts/load-env.sh
   
   # Run backup
   ./scripts/backup-postgres.sh
   
   # Or restore
   ./scripts/restore-postgres.sh -l
   ```

## Alternative: Manual Environment Setup

You can also set environment variables manually instead of using the load-env.sh script:

```bash
export POSTGRES_DB=my_database
export POSTGRES_USER=my_user
export POSTGRES_PASSWORD=my_password

./scripts/backup-postgres.sh
```

## File Locations

- Backups are stored in: `./backups/`
- Environment files: `.env`, `.env.example`
- Container name: `keycloak-postgres` (configured in docker-compose.yml)
