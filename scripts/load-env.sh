#!/bin/bash

# Environment Variable Loader Script
# This script loads environment variables from a .env file and exports them

# Function to show usage
show_usage() {
    echo "Usage: source $0 [env_file]"
    echo ""
    echo "Arguments:"
    echo "  env_file    Path to the environment file (default: .env)"
    echo ""
    echo "Examples:"
    echo "  source $0                    # Load from .env"
    echo "  source $0 .env.production    # Load from .env.production"
    echo "  source $0 config/dev.env     # Load from config/dev.env"
    echo ""
    echo "Note: This script must be sourced (not executed) to export variables to your shell"
    echo "      Use 'source load-env.sh' or '. load-env.sh'"
}

# Function to load and export environment variables
load_env_file() {
    local env_file="$1"
    
    if [ ! -f "$env_file" ]; then
        echo "Error: Environment file '$env_file' not found!"
        return 1
    fi
    
    echo "Loading environment variables from: $env_file"
    
    # Use set -a to automatically export all variables, but handle errors gracefully
    if set -a && source "$env_file" 2>/dev/null; then
        set +a
        echo "Environment variables loaded successfully"
        return 0
    else
        set +a
        echo "Error: Failed to load environment file"
        return 1
    fi
}

# Check if script is being sourced (not executed)
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    echo "Warning: This script should be sourced, not executed directly!"
    echo "Use: source $0 [env_file]"
    echo "Or:  . $0 [env_file]"
    show_usage
    exit 1
fi

# Parse command line arguments
case "$1" in
    -h|--help)
        show_usage
        return 0
        ;;
    "")
        # No argument provided, use default .env
        ENV_FILE=".env"
        ;;
    *)
        # Use provided file
        ENV_FILE="$1"
        ;;
esac

# Load the environment file
load_env_file "$ENV_FILE"
