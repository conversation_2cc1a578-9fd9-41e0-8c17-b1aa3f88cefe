#!/bin/bash

# Cucumber Gherkin Setup Validation Script
# This script validates that the Cucumber setup is working correctly

set -e

echo "🥒 Cucumber Gherkin Setup Validation"
echo "===================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 16 or higher."
    exit 1
fi

echo "✅ Node.js version: $(node --version)"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed."
    exit 1
fi

echo "✅ npm version: $(npm --version)"

# Check if Docker is running
if ! docker ps &> /dev/null; then
    echo "⚠️  Docker is not running. Please start Docker to run integration tests."
else
    echo "✅ Docker is running"
fi

# Check if Keycloak is accessible
echo "🔍 Checking Keycloak availability..."
if curl -s http://localhost:8080 > /dev/null; then
    echo "✅ Keycloak is accessible at http://localhost:8080"
else
    echo "⚠️  Keycloak is not accessible. Run 'docker-compose up -d' to start it."
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
else
    echo "✅ Dependencies already installed"
fi

# Check if Chrome is available for web tests
if command -v google-chrome &> /dev/null || command -v google-chrome-stable &> /dev/null || command -v chromium &> /dev/null; then
    echo "✅ Chrome browser found for web tests"
else
    echo "⚠️  Chrome browser not found. Web tests may fail."
fi

# Validate Cucumber configuration
echo "🧪 Validating Cucumber configuration..."
if npx cucumber-js --dry-run > /dev/null 2>&1; then
    echo "✅ Cucumber configuration is valid"
else
    echo "❌ Cucumber configuration has issues"
    exit 1
fi

# Check environment variables
echo "🔧 Environment variables:"
if [ -n "$KEYCLOAK_ADMIN" ]; then
    echo "✅ KEYCLOAK_ADMIN is set"
else
    echo "⚠️  KEYCLOAK_ADMIN not set. Using default 'admin'"
fi

if [ -n "$KEYCLOAK_ADMIN_PASSWORD" ]; then
    echo "✅ KEYCLOAK_ADMIN_PASSWORD is set"
else
    echo "⚠️  KEYCLOAK_ADMIN_PASSWORD not set. Using default 'admin'"
fi

echo ""
echo "🎉 Setup validation completed!"
echo ""
echo "Next steps:"
echo "1. Start Keycloak: docker-compose up -d"
echo "2. Load environment: source scripts/load-env.sh"
echo "3. Run tests: npm test"
echo "4. Run API tests only: npm test -- --profile api"
echo "5. Run in headless mode: HEADLESS=true npm test"
