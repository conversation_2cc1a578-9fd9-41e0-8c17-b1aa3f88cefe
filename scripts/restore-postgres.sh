#!/bin/bash

# PostgreSQL Database Restore Script
# This script restores a PostgreSQL database backup to the database running in Docker container

set -e

# Configuration
CONTAINER_NAME="keycloak-postgres"
BACKUP_DIR="./backups"

# Use exported environment variables with defaults if not set
POSTGRES_DB=${POSTGRES_DB:-keycloak}
POSTGRES_USER=${POSTGRES_USER:-postgres}
POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-password}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS] <backup_file>"
    echo ""
    echo "Options:"
    echo "  -l, --list          List available backup files"
    echo "  -f, --force         Force restore without confirmation"
    echo "  -h, --help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 -l                                    # List available backups"
    echo "  $0 postgres_backup_20240706_143022.sql.gz   # Restore from specific backup"
    echo "  $0 -f backup.sql                        # Force restore without confirmation"
}

# Function to list available backups
list_backups() {
    echo "Available backup files in $BACKUP_DIR:"
    if [ -d "$BACKUP_DIR" ]; then
        find "$BACKUP_DIR" -name "postgres_backup_*.sql*" -type f | sort -r | while read -r file; do
            filename=$(basename "$file")
            size=$(du -h "$file" | cut -f1)
            modified=$(stat -f "%Sm" -t "%Y-%m-%d %H:%M:%S" "$file" 2>/dev/null || stat -c "%y" "$file" 2>/dev/null | cut -d' ' -f1,2)
            echo "  $filename (${size}, $modified)"
        done
    else
        echo "  No backup directory found at $BACKUP_DIR"
    fi
}

# Parse command line arguments
FORCE=false
BACKUP_FILE=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -l|--list)
            list_backups
            exit 0
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        -*)
            echo "Unknown option: $1"
            show_usage
            exit 1
            ;;
        *)
            BACKUP_FILE="$1"
            shift
            ;;
    esac
done

# Check if backup file is provided
if [ -z "$BACKUP_FILE" ]; then
    echo "Error: No backup file specified."
    echo ""
    show_usage
    exit 1
fi

# Check if backup file exists (try multiple locations)
FULL_BACKUP_PATH=""
if [ -f "$BACKUP_FILE" ]; then
    FULL_BACKUP_PATH="$BACKUP_FILE"
elif [ -f "$BACKUP_DIR/$BACKUP_FILE" ]; then
    FULL_BACKUP_PATH="$BACKUP_DIR/$BACKUP_FILE"
else
    echo "Error: Backup file '$BACKUP_FILE' not found."
    echo "Checked locations:"
    echo "  - $BACKUP_FILE"
    echo "  - $BACKUP_DIR/$BACKUP_FILE"
    echo ""
    echo "Use '$0 -l' to list available backups."
    exit 1
fi

echo "PostgreSQL Database Restore"
echo "Database: $POSTGRES_DB"
echo "User: $POSTGRES_USER"
echo "Container: $CONTAINER_NAME"
echo "Backup file: $FULL_BACKUP_PATH"
echo "File size: $(du -h "$FULL_BACKUP_PATH" | cut -f1)"

# Check if container is running
if ! docker ps | grep -q "$CONTAINER_NAME"; then
    echo "Error: Container '$CONTAINER_NAME' is not running."
    echo "Please start the container with: docker-compose up -d postgres"
    exit 1
fi

# Confirmation prompt (unless force flag is used)
if [ "$FORCE" = false ]; then
    echo ""
    echo "WARNING: This will completely replace the current database content!"
    echo "All existing data in database '$POSTGRES_DB' will be lost."
    echo ""
    read -p "Are you sure you want to continue? (yes/no): " -r
    if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
        echo "Restore cancelled."
        exit 0
    fi
fi

# Determine if file is compressed
if [[ "$FULL_BACKUP_PATH" == *.gz ]]; then
    echo "Detected compressed backup file."
    echo "Restoring from compressed backup..."
    
    # Restore from compressed file
    gunzip -c "$FULL_BACKUP_PATH" | docker exec -i -e PGPASSWORD="$POSTGRES_PASSWORD" "$CONTAINER_NAME" \
        psql -U "$POSTGRES_USER" -d "$POSTGRES_DB" -v ON_ERROR_STOP=1
else
    echo "Restoring from uncompressed backup..."
    
    # Restore from uncompressed file
    docker exec -i -e PGPASSWORD="$POSTGRES_PASSWORD" "$CONTAINER_NAME" \
        psql -U "$POSTGRES_USER" -d "$POSTGRES_DB" -v ON_ERROR_STOP=1 < "$FULL_BACKUP_PATH"
fi

if [ $? -eq 0 ]; then
    echo "Database restore completed successfully!"
    echo "Restored from: $FULL_BACKUP_PATH"
else
    echo "Error: Database restore failed!"
    exit 1
fi

echo "Restore process completed!"
