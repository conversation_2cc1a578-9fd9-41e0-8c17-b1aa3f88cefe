#!/bin/bash

# PostgreSQL Database Backup Script
# This script creates a backup of the PostgreSQL database running in Docker container

set -e

# Configuration
CONTAINER_NAME="keycloak-postgres"
BACKUP_DIR="./backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Use exported environment variables with defaults if not set
POSTGRES_DB=${POSTGRES_DB:-keycloak}
POSTGRES_USER=${POSTGRES_USER:-postgres}
POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-password}

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

# Generate backup filename
BACKUP_FILE="$BACKUP_DIR/postgres_backup_${TIMESTAMP}.sql"

echo "Starting PostgreSQL backup..."
echo "Database: $POSTGRES_DB"
echo "User: $POSTGRES_USER"
echo "Container: $CONTAINER_NAME"
echo "Backup file: $BACKUP_FILE"

# Check if container is running
if ! docker ps | grep -q "$CONTAINER_NAME"; then
    echo "Error: Container '$CONTAINER_NAME' is not running."
    echo "Please start the container with: docker-compose up -d postgres"
    exit 1
fi

# Create the backup using pg_dump
echo "Creating backup..."
docker exec -e PGPASSWORD="$POSTGRES_PASSWORD" "$CONTAINER_NAME" \
    pg_dump -U "$POSTGRES_USER" -d "$POSTGRES_DB" --verbose --clean --if-exists > "$BACKUP_FILE"

if [ $? -eq 0 ]; then
    echo "Backup completed successfully!"
    echo "Backup file: $BACKUP_FILE"
    echo "File size: $(du -h "$BACKUP_FILE" | cut -f1)"
    
    # Compress the backup
    echo "Compressing backup..."
    gzip "$BACKUP_FILE"
    echo "Compressed backup: ${BACKUP_FILE}.gz"
    echo "Compressed size: $(du -h "${BACKUP_FILE}.gz" | cut -f1)"
else
    echo "Error: Backup failed!"
    exit 1
fi

# Optional: Clean up old backups (keep last 7 days)
echo "Cleaning up old backups (keeping last 7 days)..."
find "$BACKUP_DIR" -name "postgres_backup_*.sql.gz" -type f -mtime +7 -delete

echo "Backup process completed!"
