# Docker Compose Project - .dockerignore
# This file excludes unnecessary files from Docker build context

# Version Control
.git/
.gitignore
.gitattributes
.gitmodules

# Documentation
README.md
*.md
docs/
documentation/

# Docker files (avoid recursive copying)
Dockerfile*
docker-compose*.yml
.dockerignore

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~
.vim/
.emacs.d/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Temporary and log files
*.log
*.tmp
*.temp
*.pid
*.swp
*.backup
*.bak
.cache/
tmp/
temp/

# Node.js (if you add frontend components)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Python (if you add Python scripts)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.venv/
.env

# Java (if you add Java components)
*.class
*.jar
*.war
*.ear
target/
.mvn/

# Build artifacts and dependencies
build/
dist/
out/
bin/
lib/
vendor/

# Database files (local development)
*.db
*.sqlite
*.sqlite3
pgdata/
mysql-data/

# Keycloak specific
keycloak-data/
standalone/
domain/

# SSL certificates and keys
*.pem
*.key
*.crt
*.cert
*.p12
*.jks

# Environment and configuration
.env*
config.local.*
settings.local.*
local.properties

# Testing
coverage/
.nyc_output/
test-results/
.pytest_cache/

# Backup files
*.backup
*.dump
*.sql.gz

# Compressed files
*.zip
*.tar
*.tar.gz
*.rar
*.7z

# Backup directories
backups/
