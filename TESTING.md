# Cucumber Gherkin API Testing for Keycloak

This project includes comprehensive Cucumber Gherkin test scenarios for testing Keycloak REST API functionality.

## 🚀 Quick Start

### Prerequisites

1. **Docker & Docker Compose**: Ensure Keycloak is running
2. **Node.js**: Version 16 or higher

### Setup

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Start Keycloak** (if not already running):
   ```bash
   docker-compose up -d
   ```

3. **Set environment variables**:
   ```bash
   source scripts/load-env.sh
   ```

## 🧪 Running Tests

### Run All Tests
```bash
npm test
```

### Run Tests for CI/CD
```bash
npm run test:ci
```

### Generate Reports
```bash
npm run test:report
```

## 📁 Project Structure

```
features/
├── keycloak-api.feature           # API testing scenarios
├── step_definitions/              # Step implementations
│   └── api-steps.js              # API testing steps
└── support/                      # Test configuration
    └── world.js                  # World constructor
```

## 🎯 Features Covered

### Keycloak REST API (`keycloak-api.feature`)
- ✅ Get realm information
- ✅ List all realms
- ✅ Get users in realm
- ✅ Get clients in realm
- ✅ Create new user via API
- ✅ Handle invalid API requests
- ✅ Test unauthorized access

## 🔧 Configuration

### Environment Variables

Required environment variables (set via `.env` or exported):

```bash
KEYCLOAK_ADMIN=admin
KEYCLOAK_ADMIN_PASSWORD=your_password
KEYCLOAK_BASE_URL=http://localhost:8080
```

### Cucumber Profiles

The project includes test profiles defined in `cucumber.js`:

- **default**: All tests with HTML and JSON reports
- **ci**: Optimized for CI/CD with JSON reports only

### Tags

Use tags to run specific test groups:

- `@api` - REST API tests

## 📊 Reports

Test reports are generated in the `reports/` directory:

- `cucumber-report.html` - Interactive HTML report
- `cucumber-report.json` - JSON format for CI integration

## 🔍 Writing New Tests

### 1. Create Feature Files

```gherkin
Feature: New API Feature
  As a developer
  I want to test specific API endpoints
  So that I can verify functionality

  @api
  Scenario: Test scenario
    Given I have valid admin credentials
    When I make a GET request to "/admin/some-endpoint"
    Then the response status should be 200
```

### 2. Implement Step Definitions

```javascript
const { Given, When, Then } = require('@cucumber/cucumber');

When('I make a GET request to {string}', async function (endpoint) {
  // Implementation
});

Then('the response status should be {int}', function (expectedStatus) {
  // Assertions
});
```

### 3. Best Practices

- **Use descriptive scenario names**
- **Keep steps reusable**
- **Use data tables for complex data**
- **Tag scenarios with @api**
- **Add proper error handling**

## 🚨 Troubleshooting

### Common Issues

1. **Keycloak not accessible**:
   ```bash
   # Check if Keycloak is running
   docker-compose ps
   curl http://localhost:8080
   ```

2. **Environment variables not loaded**:
   ```bash
   # Ensure .env file exists and is loaded
   source scripts/load-env.sh
   echo $KEYCLOAK_ADMIN
   ```

3. **Test timeouts**:
   - Increase timeout in `world.js`
   - Check Keycloak performance

### Debug Mode

Run tests with debug output:

```bash
DEBUG=* npm test
```

## 🔄 CI/CD Integration

### GitHub Actions Example

```yaml
- name: Run Cucumber API Tests
  run: |
    npm install
    npm run test:ci
  env:
    KEYCLOAK_ADMIN: admin
    KEYCLOAK_ADMIN_PASSWORD: ${{ secrets.KEYCLOAK_PASSWORD }}
```

### Jenkins Example

```groovy
stage('API Tests') {
    steps {
        sh '''
            npm install
            npm run test:ci
        '''
    }
    post {
        always {
            publishHTML([
                allowMissing: false,
                alwaysLinkToLastBuild: true,
                keepAll: true,
                reportDir: 'reports',
                reportFiles: 'cucumber-report.html',
                reportName: 'Cucumber API Report'
            ])
        }
    }
}
```

## 📚 Resources

- [Cucumber.js Documentation](https://cucumber.io/docs/cucumber/)
- [Gherkin Reference](https://cucumber.io/docs/gherkin/reference/)
- [Keycloak Admin REST API](https://www.keycloak.org/docs-api/23.0.0/rest-api/index.html)
- [Axios HTTP Client](https://axios-http.com/docs/intro)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add your tests
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
