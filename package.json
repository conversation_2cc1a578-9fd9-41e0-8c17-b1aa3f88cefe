{"name": "acme-code-keycloak-testing", "version": "1.0.0", "description": "Keycloak integration testing with Cucumber Gherkin", "main": "index.js", "scripts": {"test": "cucumber-js", "test:watch": "cucumber-js --watch", "test:report": "cucumber-js --format html:reports/cucumber-report.html", "test:ci": "cucumber-js --format json:reports/cucumber-report.json"}, "keywords": ["keycloak", "testing", "cucumber", "g<PERSON>kin", "bdd"], "author": "", "license": "MIT", "devDependencies": {"@cucumber/cucumber": "^10.0.0", "axios": "^1.6.0", "chai": "^4.3.0"}, "cucumber": {"require": ["features/support/**/*.js"], "format": ["progress-bar", "html:reports/cucumber-report.html"], "formatOptions": {"snippetInterface": "async-await"}}}