# Keycloak with PostgreSQL - Learning Setup

This project provides Docker Compose configurations for learning Keycloak with PostgreSQL, using environment variables for secure configuration.

## Environment Configuration

The project uses environment variables for all sensitive data. You can provide these in two ways:

### Option 1: Using .env file (recommended)
Copy `.env.example` to `.env` and fill in your values:
```bash
cp .env.example .env
# Edit .env with your preferred values
```

### Option 2: Export variables before starting VS Code
```bash
export POSTGRES_DB=keycloak
export POSTGRES_USER=keycloak
export POSTGRES_PASSWORD=your_secure_password
export KEYCLOAK_ADMIN=admin
export KEYCLOAK_ADMIN_PASSWORD=your_admin_password
export KC_DB_USERNAME=keycloak
export KC_DB_PASSWORD=your_secure_password
export KC_LOG_LEVEL=info

# Then start VS Code - variables will be inherited
code .
```

## Required Environment Variables

- `POSTGRES_DB` - PostgreSQL database name
- `POSTGRES_USER` - PostgreSQL username  
- `POSTGRES_PASSWORD` - PostgreSQL password
- `<PERSON>E<PERSON>CLOAK_ADMIN` - Keycloak admin username
- `KEYCLOAK_ADMIN_PASSWORD` - Keycloak admin password
- `KC_DB_USERNAME` - Keycloak database username (usually same as POSTGRES_USER)
- `KC_DB_PASSWORD` - Keycloak database password (usually same as POSTGRES_PASSWORD)
- `KC_LOG_LEVEL` - Logging level (optional, defaults to info)

## Services

- **Keycloak**: http://localhost:8080
- **PostgreSQL**: localhost:5432
- Uses environment variables for all credentials

## Quick Start

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f keycloak

# Stop all services
docker-compose down

# Stop and remove volumes (fresh start)
docker-compose down -v
```

## Learning Features Enabled

- Token exchange
- Fine-grained authorization  
- Health and metrics endpoints
- Development mode (relaxed security)
- Debug logging

## Useful Commands

```bash
# View service status
docker-compose ps

# Access Keycloak container shell
docker exec -it keycloak bash

# Access PostgreSQL directly
docker exec -it keycloak-postgres psql -U ${POSTGRES_USER} -d ${POSTGRES_DB}

# Check environment variables are loaded
docker-compose config
```

## Troubleshooting

- **Environment variables not working**: Ensure they're set before starting VS Code or use a `.env` file
- **Port conflicts**: Change port mappings in docker-compose.yml
- **Connection issues**: Check container logs with `docker-compose logs [service-name]`
- **Database connection**: Wait for health checks to pass before accessing services

## Security Notes

- Never commit `.env` files to version control
- Use strong passwords in production
- The `.env.example` file shows required variables without exposing secrets
- Environment variables set before starting VS Code are inherited by Docker Compose

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
